import { Header } from "@/layouts";
import React from "react";
import KYCContent from "./parts/KycContent";
import { cookies } from "next/headers";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants/common";
import config from "@/config/index";

async function createAccessToken(kycLevel?: string) {
  const accessToken =
    (await cookies()).get(COOKIES_ACCESS_TOKEN_KEY)?.value || "";

  try {
    const url = `${config.apiUrl}/v1/kyc/verification`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        ...(kycLevel && { levelName: kycLevel }),
      }),
      cache: "no-store",
    });

    console.log("🔄 Creating Sumsub access token for level:", kycLevel);

    if (!res.ok) {
      const errorText = await res.text();
      throw new Error(`Failed to get token: ${errorText}`);
    }

    const data = await res.json();
    console.log("✅ Sumsub access token created successfully");
    return data;
  } catch (e: any) {
    console.error("❌ Error creating access token:", e?.message);
  }
}

interface KYCPageProps {
  searchParams: {
    level?: string;
    reset?: string;
  };
}

export default async function KYCPage({ searchParams }: KYCPageProps) {
  const kycLevel = searchParams.level || "basic";
  const forceReset = searchParams.reset === "true";

  const data = await createAccessToken(kycLevel);

  return (
    <div>
      <Header />
      <KYCContent
        accessToken={data?.sumsubAccessToken}
        kycLevel={kycLevel}
        forceReset={forceReset}
      />
    </div>
  );
}
