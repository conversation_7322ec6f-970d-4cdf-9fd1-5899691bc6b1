"use client";

import React, { useEffect, useMemo, useState } from "react";
import Image from "next/image";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import rf from "@/services/RequestFactory";
import { AppButton } from "@/components";
import {
  QuestionIcon,
  HeadphoneIcon,
  CopyIcon,
  CheckBrokenIcon,
} from "@/assets/icons";
import { KYC_STATUS } from "@/constants/common";
import { TKYCDetails } from "@/types/kyc";
import Link from "next/link";
import { copyToClipboard } from "@/utils/helper";
import { maskCardId, maskEmail } from "@/utils/format";
import { KYC_LEVEL } from "@/constants/common";
import { formatNumber } from "@/utils/format";

const VerifiedContent = () => {
  const [kycDetails, setKycDetails] = useState<TKYCDetails>();
  const [withdrawLimits, setWithdrawLimits] = useState<any[]>([]);
  const userInfo = useSelector((state: RootState) => state.user.userInfo);

  const getKycDetails = async () => {
    try {
      const res = await rf.getRequest("KYCRequest").getKYCDetails();
      setKycDetails(res);
    } catch (e) {
      console.error(e, "getKycDetails Error");
    }
  };

  const getWithdrawLimits = async () => {
    try {
      const res = await rf.getRequest("AccountRequest").getAllWithdrawLimits();
      setWithdrawLimits(res);
    } catch (e) {
      console.error(e, "getWithdrawLimits Error");
    }
  };

  useEffect(() => {
    if (!userInfo?.id) return;
    getKycDetails().then();
    getWithdrawLimits().then();
  }, [userInfo]);

  const accountLimitCurrent = useMemo(
    () =>
      withdrawLimits.find((item: any) => {
        return item.kycLevelName === userInfo?.kycLevel;
      }),
    [userInfo?.kycLevel, withdrawLimits]
  );

  const accountLimitLevelAdvance = useMemo(
    () =>
      withdrawLimits.find((item: any) => {
        return item.kycLevelName === KYC_LEVEL.ADVANCE;
      }),
    [withdrawLimits]
  );

  const accountLimitLevelBasic = useMemo(
    () =>
      withdrawLimits.find((item: any) => {
        return item.kycLevelName === KYC_LEVEL.BASIC;
      }),
    [withdrawLimits]
  );

  return (
    <div className="flex w-full gap-[56px]">
      <div className="flex flex-1 flex-col lg:gap-6">
        {userInfo?.kycLevel === KYC_LEVEL.BASIC && (
          <div className="flex flex-col gap-4 p-4 lg:hidden">
            <div className="heading-lg-medium-24">
              Upgrade ti increase your fiat limit to{" "}
              {formatNumber(accountLimitLevelAdvance?.withdrawLimit || 0)} USD
              daily
            </div>
            <div>
              <div className="body-sm-medium-12 text-white-500 ">Required:</div>
              <ul className="list-disc pl-4">
                <li className="body-sm-regular-12 text-white-500 mt-2">
                  Proof of address
                </li>
              </ul>
            </div>
            <Link href={"/my/kyc?level=advance&reset=true"}>
              <AppButton variant="buy" size="large" className="w-full">
                Get Verified Plus
              </AppButton>
            </Link>
          </div>
        )}

        <div className="border-white-50 border-b">
          <div className="heading-md-medium-18 px-4 py-2 lg:px-0">
            Account Limits
          </div>
          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Crypto Deposit Limit</div>
            <div>Unlimited</div>
          </div>
          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Crypto Withdraw Limit</div>
            <div>
              {formatNumber(accountLimitCurrent?.withdrawLimit || 0)} USDT Daily
            </div>
          </div>
        </div>
        <div>
          <div className="flex items-center justify-between px-4 lg:px-0">
            <div className="heading-md-medium-18 py-2">
              Personal information
            </div>
          </div>

          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Country of Residence</div>
            <div>{kycDetails?.country || "--"}</div>
          </div>
          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Legal Name</div>
            <div className="flex items-center gap-2">
              {kycDetails?.firstName || "--"} {kycDetails?.lastName || ""}
            </div>
          </div>
          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Date of Birth</div>
            <div>{kycDetails?.dob || "--"}</div>
          </div>
          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Identification Document</div>
            <div>
              ID Card, {maskCardId(kycDetails?.identificationNumber || "")}
            </div>
          </div>
          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Address</div>
            <div className="flex items-center gap-2">--</div>
          </div>
          <div className="body-md-regular-14 flex justify-between p-4">
            <div className="text-white-500">Email</div>
            <div>{maskEmail(userInfo?.email || "")}</div>
          </div>
        </div>
      </div>

      <div className="hidden px-4 lg:block">
        <div className="mb-2 py-2">Verification Levels</div>

        <div className="border-white-100 flex gap-4 border-b pb-4 pl-4">
          <div className="flex flex-col items-center gap-1">
            <CheckBrokenIcon className="text-green-500" />
            <div className="h-[40px] w-[1px] bg-green-500"></div>
            <CheckBrokenIcon
              className={`${
                userInfo?.kycLevel === KYC_LEVEL.ADVANCE
                  ? "text-green-500"
                  : "text-white-500"
              }`}
            />
          </div>
          <div className="flex flex-col gap-4 pl-4">
            <div>
              <div className="heading-sm-medium-16 text-green-500">
                Verified
              </div>
              <div className="body-sm-medium-12 mt-2">
                Withdrawal limits up to{" "}
                {formatNumber(accountLimitLevelBasic?.withdrawLimit || 0)} USD
                daily
              </div>
            </div>
            <div>
              <div
                className={`heading-sm-medium-16 text-green-500 ${
                  userInfo?.kycLevel === KYC_LEVEL.ADVANCE
                    ? "text-green-500"
                    : "text-white-500"
                }`}
              >
                Verified Plus
              </div>

              <div className="body-sm-medium-12 mt-2">
                Withdrawal limits up to{" "}
                {formatNumber(accountLimitLevelAdvance?.withdrawLimit || 0)} USD
                daily
              </div>

              {userInfo?.kycLevel === KYC_LEVEL.BASIC && (
                <Link href={"/my/kyc?level=advance&reset=true"}>
                  <AppButton variant="buy" size="small" className="mt-2 w-max">
                    Get Verified Plus
                  </AppButton>
                </Link>
              )}
            </div>
          </div>
        </div>

        <div className="mt-4">
          <div className="heading-md-medium-18 mb-2">FAQ</div>
          <Link
            href="#"
            className="body-md-regular-14 text-green-500 underline"
          >
            Identity Verification
          </Link>
        </div>
      </div>
    </div>
  );
};

export default function IdentificationPage() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);

  const _renderContentVerify = () => {
    if (!userInfo.kycStatus) {
      return (
        <div className="body-md-regular-14 mt-10 text-center">Loading...</div>
      );
    }

    if (userInfo.kycStatus === KYC_STATUS.UNVERIFIED) {
      return (
        <div className="bg-white-50 border-white-100 mx-auto w-max rounded-[16px] border p-4 lg:mx-0">
          <div className="heading-lg-medium-24 mb-2">Identity Verification</div>
          <div className="body-md-regular-14 text-white-500 max-w-[340px]">
            Complete identity verification to access all VDAX service
          </div>

          <Link href="/my/kyc?level=basic&reset=true">
            <AppButton variant="buy" size="large" className="my-3 w-full">
              Get Verified
            </AppButton>
          </Link>

          <div className="flex justify-center gap-2">
            <div className="flex cursor-pointer items-center gap-1 px-2">
              <HeadphoneIcon />
              <div className="body-xs-medium-10">Need Help?</div>
            </div>
            <div className="flex cursor-pointer items-center gap-1 px-2">
              <QuestionIcon />
              <div className="body-xs-medium-10">Identify Verification FAQ</div>
            </div>
          </div>
        </div>
      );
    }

    return <VerifiedContent />;
  };

  return (
    <div className="flex w-full flex-col gap-4 lg:gap-8">
      <div className="border-white-50 flex flex-col items-center gap-4 border-b p-4 lg:flex-row">
        {userInfo.avatar ? (
          <img
            src={userInfo.avatar}
            alt="avatar"
            className="aspect-square h-[80px] !w-[80px] rounded-full lg:rounded-[12px]"
          />
        ) : (
          <Image
            src={"/images/AvatarDefault.png"}
            alt="avatar"
            width={80}
            height={80}
            className="aspect-square h-[80px] !w-[80px] rounded-full lg:rounded-[12px]"
          />
        )}
        <div className="flex flex-col items-center lg:items-start">
          <div className="heading-md-medium-18 mb-2">
            {userInfo?.name || userInfo?.email}
          </div>

          <div className="body-md-medium-14 mb-2 flex items-center gap-2">
            ID: {userInfo?.id}
            <CopyIcon
              className="cursor-pointer"
              onClick={() => copyToClipboard(userInfo?.id?.toString() || "")}
            />
          </div>
          <div
            className={`body-xs-medium-10 mb-1 w-max rounded-[4px] px-2 py-1 capitalize ${
              userInfo?.kycStatus !== KYC_STATUS.VERIFIED
                ? "bg-white-100"
                : "bg-green-900 text-green-500"
            }`}
          >
            {userInfo?.kycStatus || "--"}
          </div>
        </div>
      </div>

      {_renderContentVerify()}
    </div>
  );
}
