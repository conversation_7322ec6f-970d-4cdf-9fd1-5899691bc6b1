"use client";

import {
  CopyIcon,
  // ChevronDownIcon,
  // EditIcon
} from "@/assets/icons";
import { copyToClipboard } from "@/utils/helper";
import { AppButton } from "@/components";
import React from "react";
import { TableMarkets } from "./TableMarket";
import { MyBalance } from "../overview/_parts/MyBalance";
import Image from "next/image";
import { useSelector } from "react-redux";
import { RootState } from "@/store/index";
import { KYC_STATUS } from "@/constants/common";
import Link from "next/link";
import { maskEmail } from "@/utils/format";

const CardKYC = () => {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);

  if (userInfo.kycStatus === KYC_STATUS.VERIFIED) return <></>;
  if (userInfo.kycStatus === KYC_STATUS.FAILED) {
    return (
      <div className="border-white-100 bg-white-50 flex w-max gap-4 rounded-[16px] border p-4 hover:border-green-500 hover:bg-green-900">
        <div className="flex flex-col justify-between">
          <div>
            <div className="heading-lg-medium-24 mb-1.5 text-red-400">
              Verification Failed
            </div>
            <div className="body-md-regular-14 text-white-500 max-w-[135px] lg:max-w-[166px]">
              Please view the reasons and resubmit when you are ready
            </div>
          </div>
          <Link href="/my/kyc?reset=true">
            <AppButton variant="buy" className="w-max">
              View Details
            </AppButton>
          </Link>
        </div>
        <Image
          src={"/images/Card.png"}
          alt="card"
          width={160}
          height={160}
          className="aspect-square h-[160px] w-[160px]"
        />
      </div>
    );
  }

  return (
    <div className="border-white-100 bg-white-50 flex w-max gap-4 rounded-[16px] border p-4 hover:border-green-500 hover:bg-green-900">
      <div className="flex flex-col justify-between">
        <div>
          <div className="heading-lg-medium-24 mb-1.5">Verify Account</div>
          <div className="body-md-regular-14 text-white-500 max-w-[135px] lg:max-w-[166px]">
            Complete identity verification to access all VDAX service
          </div>
        </div>
        {userInfo.kycStatus === KYC_STATUS.PENDING ? (
          <AppButton variant="buy" className="w-max" disabled>
            Verifying
          </AppButton>
        ) : (
          <Link href="/my/kyc?level=basic&reset=true">
            <AppButton variant="buy" className="w-max">
              Verify Now
            </AppButton>
          </Link>
        )}
      </div>
      <Image
        src={"/images/Card.png"}
        alt="card"
        width={160}
        height={160}
        className="aspect-square h-[160px] w-[160px]"
      />
    </div>
  );
};

export default function DashboardPage() {
  const userInfo = useSelector((state: RootState) => state.user.userInfo);

  return (
    <div className="flex flex-col overflow-x-hidden lg:gap-8">
      <div className="flex items-center justify-between p-4 py-2 lg:justify-start lg:gap-[82px] lg:py-4">
        <div className="border-white-100 flex gap-4 lg:border-r lg:pr-[82px]">
          <Image
            src={"/images/AvatarDefault.png"}
            alt="avatar"
            width={64}
            height={64}
            className="aspect-square h-[64px] !w-[64px] rounded-[8px]"
          />
          <div className="flex flex-col justify-center">
            <div className="lg:heading-lg-medium-24 text-[16px]">
              {userInfo?.name || "--"}
            </div>
            <div className="flex items-center gap-2">
              {maskEmail(userInfo?.email || "--")}
            </div>
          </div>
        </div>

        {/*<div className="flex gap-4 lg:hidden">*/}
        {/*  <AppButton size="medium" variant="outline">*/}
        {/*    <EditIcon />*/}
        {/*  </AppButton>*/}
        {/*  <AppButton size="medium" variant="outline" className="w-9">*/}
        {/*    <ChevronDownIcon />*/}
        {/*  </AppButton>*/}
        {/*</div>*/}
        <div className="hidden lg:block">
          <div className="text-white-500 body-md-regular-14 mb-1">UID</div>
          <div className="body-md-medium-14 flex items-center gap-2">
            {userInfo?.id}
            <CopyIcon
              onClick={() => copyToClipboard(userInfo?.id?.toString())}
              className="cursor-pointer"
            />
          </div>
        </div>
        <div className="hidden lg:block">
          <div className="text-white-500 body-md-regular-14 mb-1">
            VIP Level
          </div>
          <div className="body-md-medium-14 flex items-center gap-2">
            Regular User
            {/* <ChevronDownIcon className="text-white-500 rotate-[-90deg] cursor-pointer" /> */}
          </div>
        </div>
      </div>

      <div className="overflow-x-auto ">
        <div className="flex w-max gap-4 px-4 py-2 lg:px-0 lg:py-0">
          <CardKYC />

          <div className="border-white-100 bg-white-50 flex w-max gap-4 rounded-[16px] border p-4 hover:border-green-500 hover:bg-green-900">
            <div className="flex flex-col justify-between">
              <div>
                <div className="heading-lg-medium-24 mb-1.5">Deposit</div>
                <div className="body-md-regular-14 text-white-500 max-w-[135px] lg:max-w-[166px]">
                  Start deposit cash and investment
                </div>
              </div>

              <Link href="/my/deposit">
                <AppButton variant="buy" className="w-max">
                  Deposit Now
                </AppButton>
              </Link>
            </div>
            <Image
              src={"/images/DepositCard.svg"}
              alt="card"
              width={160}
              height={160}
              className="aspect-square h-[160px] w-[160px]"
            />
          </div>
        </div>
      </div>

      {/* Balance */}
      <div>
        <MyBalance />
      </div>

      <div className="border-white-100 p-4 lg:rounded-[16px] lg:border">
        <div className="mb-2 flex items-center justify-between">
          <div className="lg:text-white-500 heading-sm-medium-16">Markets</div>
          {/*<div className="body-sm-medium-12 flex cursor-pointer items-center gap-2 lg:hidden ">*/}
          {/*  More <ChevronDownIcon className="rotate-[-90deg]" />*/}
          {/*</div>*/}
        </div>

        <TableMarkets />
      </div>
    </div>
  );
}
