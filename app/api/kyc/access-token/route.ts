import config from "@/config";
import { COOKIES_ACCESS_TOKEN_KEY } from "@/constants";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";

export async function POST(request: Request) {
  try {
    const { kycLevel } = await request.json();
    const cookieStore = await cookies();
    const accessToken = cookieStore.get(COOKIES_ACCESS_TOKEN_KEY)?.value || "";

    if (!accessToken) {
      return NextResponse.json(
        { success: false, error: "Authentication required" },
        { status: 401 }
      );
    }

    console.log("🔄 Creating new Sumsub access token for level:", kycLevel);

    const url = `${config.apiUrl}/v1/kyc/verification`;
    const res = await fetch(url, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        ...(kycLevel && { levelName: kycLevel }),
      }),
      cache: "no-store",
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.error("❌ Failed to create access token:", errorText);
      return NextResponse.json(
        { success: false, error: `Failed to get token: ${errorText}` },
        { status: res.status }
      );
    }

    const data = await res.json();
    console.log("✅ New Sumsub access token created successfully");

    return NextResponse.json({
      success: true,
      sumsubAccessToken: data.sumsubAccessToken,
    });
  } catch (error: any) {
    console.error("❌ Error creating access token:", error.message);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
