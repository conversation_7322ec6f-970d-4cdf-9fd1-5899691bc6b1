import { useCallback, useRef } from 'react';

/**
 * Hook to manage Sumsub SDK resets and reinitialization
 * This helps ensure the SDK starts fresh when transitioning between verification levels
 */
export const useSumsubSDKReset = () => {
  const resetCounter = useRef(0);

  /**
   * Force a complete SDK reset by generating a new unique key
   * This should be called when:
   * - Transitioning from basic to advanced KYC
   * - User encounters errors and needs to restart
   * - Session expires and needs fresh initialization
   */
  const forceSDKReset = useCallback(() => {
    resetCounter.current += 1;
    console.log('🔄 Forcing Sumsub SDK reset, new counter:', resetCounter.current);
    return resetCounter.current;
  }, []);

  /**
   * Generate a unique SDK key for React component mounting
   * This key changes whenever a reset is forced
   */
  const getSDKKey = useCallback((kycLevel?: string) => {
    return `sumsub-sdk-${resetCounter.current}-${kycLevel || 'default'}-${Date.now()}`;
  }, []);

  /**
   * Clear any cached SDK data from localStorage/sessionStorage
   * This helps ensure no residual data affects the new session
   */
  const clearSDKCache = useCallback(() => {
    try {
      // Clear Sumsub-related items from storage
      const keysToRemove = [];
      
      // Check localStorage
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('sumsub') || key.includes('idensic'))) {
          keysToRemove.push(key);
        }
      }
      
      // Remove identified keys
      keysToRemove.forEach(key => {
        localStorage.removeItem(key);
        console.log('🧹 Cleared localStorage key:', key);
      });

      // Clear sessionStorage as well
      const sessionKeysToRemove = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.includes('sumsub') || key.includes('idensic'))) {
          sessionKeysToRemove.push(key);
        }
      }
      
      sessionKeysToRemove.forEach(key => {
        sessionStorage.removeItem(key);
        console.log('🧹 Cleared sessionStorage key:', key);
      });

      console.log('✅ SDK cache cleared successfully');
    } catch (error) {
      console.warn('⚠️ Failed to clear SDK cache:', error);
    }
  }, []);

  /**
   * Complete SDK reset including cache clearing and key regeneration
   */
  const performCompleteReset = useCallback(() => {
    clearSDKCache();
    return forceSDKReset();
  }, [clearSDKCache, forceSDKReset]);

  return {
    forceSDKReset,
    getSDKKey,
    clearSDKCache,
    performCompleteReset,
    resetCounter: resetCounter.current,
  };
};

export default useSumsubSDKReset;
