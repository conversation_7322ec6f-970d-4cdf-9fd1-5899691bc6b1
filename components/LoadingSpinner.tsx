import React from "react";
import clsx from "clsx";

interface LoadingSpinnerProps {
  size?: "small" | "medium" | "large";
  className?: string;
  message?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "medium",
  className = "",
  message = "Loading...",
}) => {
  const getSizeClasses = () => {
    switch (size) {
      case "small":
        return "w-4 h-4 border-2";
      case "large":
        return "w-12 h-12 border-4";
      default:
        return "w-8 h-8 border-2";
    }
  };

  return (
    <div className={clsx("flex flex-col items-center justify-center", className)}>
      <div
        className={clsx(
          "animate-spin rounded-full border-solid border-white-200 border-t-white-1000",
          getSizeClasses()
        )}
      />
      {message && (
        <div className="body-md-regular-14 text-white-500 mt-3 text-center">
          {message}
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
