/**
 * Test script to verify Sumsub SDK reset functionality
 * Run this in browser console to test the reset mechanisms
 */

// Test 1: Verify SDK key generation
console.log('🧪 Testing SDK key generation...');

// Simulate the hook behavior
let resetCounter = 0;
const getSDKKey = (kycLevel) => {
  return `sumsub-sdk-${resetCounter}-${kycLevel || 'default'}-${Date.now()}`;
};

const forceSDKReset = () => {
  resetCounter += 1;
  console.log('🔄 Forcing SDK reset, new counter:', resetCounter);
  return resetCounter;
};

// Test key generation
const key1 = getSDKKey('basic');
const key2 = getSDKKey('basic');
console.log('Key 1:', key1);
console.log('Key 2:', key2);
console.log('Keys are different:', key1 !== key2);

// Test reset functionality
forceSDKReset();
const key3 = getSDKKey('advance');
console.log('Key 3 after reset:', key3);

// Test 2: Verify cache clearing simulation
console.log('\n🧪 Testing cache clearing simulation...');

// Simulate adding Sumsub-related items to localStorage
localStorage.setItem('sumsub-test-item', 'test-value');
localStorage.setItem('idensic-test-item', 'test-value');
localStorage.setItem('other-item', 'should-remain');

console.log('Before clearing:');
console.log('localStorage length:', localStorage.length);

// Simulate cache clearing
const keysToRemove = [];
for (let i = 0; i < localStorage.length; i++) {
  const key = localStorage.key(i);
  if (key && (key.includes('sumsub') || key.includes('idensic'))) {
    keysToRemove.push(key);
  }
}

keysToRemove.forEach(key => {
  localStorage.removeItem(key);
  console.log('🧹 Cleared localStorage key:', key);
});

console.log('After clearing:');
console.log('localStorage length:', localStorage.length);
console.log('Other item still exists:', localStorage.getItem('other-item') !== null);

// Test 3: Verify URL parameter parsing
console.log('\n🧪 Testing URL parameter parsing...');

const testUrls = [
  '/my/kyc?level=basic&reset=true',
  '/my/kyc?level=advance&reset=true',
  '/my/kyc?reset=true',
  '/my/kyc?level=basic',
  '/my/kyc'
];

testUrls.forEach(url => {
  const urlParams = new URLSearchParams(url.split('?')[1] || '');
  const level = urlParams.get('level') || 'basic';
  const reset = urlParams.get('reset') === 'true';
  
  console.log(`URL: ${url}`);
  console.log(`  Level: ${level}, Reset: ${reset}`);
});

// Test 4: Verify React key generation logic
console.log('\n🧪 Testing React key generation logic...');

const scenarios = [
  { kycLevel: 'basic', forceReset: false },
  { kycLevel: 'basic', forceReset: true },
  { kycLevel: 'advance', forceReset: false },
  { kycLevel: 'advance', forceReset: true },
];

scenarios.forEach((scenario, index) => {
  if (scenario.forceReset) {
    forceSDKReset();
  }
  
  const key = getSDKKey(scenario.kycLevel);
  console.log(`Scenario ${index + 1}:`, scenario, '-> Key:', key);
});

console.log('\n✅ All tests completed successfully!');
console.log('\n📋 Summary:');
console.log('- SDK key generation works correctly');
console.log('- Cache clearing simulation successful');
console.log('- URL parameter parsing verified');
console.log('- React key logic validated');

// Cleanup test items
localStorage.removeItem('other-item');
